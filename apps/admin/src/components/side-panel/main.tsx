import LogoWithoutBorder from '@/assets/logos/logo-without-border.svg';
import { Button } from '@/components/ui/button';
import { Sidebar } from '@/components/ui/sidebar';
import { cookie } from '@/lib/cookie';
import { useBaseStore } from '@/store/base-store';
import { clearCurrentGitWorkspace } from '@/utils/git-utils';
import { Folder, LogOut, Plus, Settings } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const { clearAll, workspaceId } = useBaseStore((state) => ({
    clearAll: state.clearAll,
    workspaceId: state.workspaceId,
  }));

  return (
    <Sidebar collapsible="icon" {...props}>
      {/* Fox Logo */}
      <div className="flex flex-col items-center justify-between h-full p-4 bg-[#090D12]">
        <button onClick={() => navigate('/')}>
          <img width={25} height={25} src={LogoWithoutBorder} alt="" />
        </button>

        {/* Navigation Icons */}
        <div className="flex-1 flex flex-col items-center justify-center space-y-4">
          <Button
            variant="ghost"
            size="icon"
            className={`w-10 h-10 rounded-lg  hover:bg-[#1d2127]`}
          >
            <Plus className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate('/projects')}
            className={`w-10 h-10 rounded-lg ${pathname === '/projects' ? 'text-[#ff712f] bg-[#1d2127]' : 'text-gray-400 hover:text-gray-300'} hover:bg-[#1d2127]`}
          >
            <Folder className="w-5 h-5" />
          </Button>
          {/* <Button
            variant="ghost"
            size="icon"
            onClick={() => setActiveNavItem('info')}
            className={`w-10 h-10 rounded-lg ${activeNavItem === 'info' ? 'text-[#ff712f] bg-[#1d2127]' : 'text-gray-400 hover:text-gray-300'} hover:bg-[#1d2127]`}
          >
            <Info className="w-5 h-5" />
          </Button> */}
        </div>

        {/* Bottom Icons */}
        <div className="flex flex-col items-center space-y-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate('/profile')}
            className={`w-10 h-10 rounded-lg ${pathname === '/profile' ? 'text-[#ff712f] bg-[#1d2127]' : 'text-gray-400 hover:text-gray-300'} hover:bg-[#1d2127]`}
          >
            <Settings className="w-5 h-5" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              cookie.remove('access_token');

              // Clear current workspace git storage
              clearCurrentGitWorkspace(workspaceId);

              // Clear all store state
              clearAll();

              setTimeout(() => {
                navigate('/logout');
              }, 250);
            }}
            className={`w-10 h-10 rounded-lg  hover:bg-[#1d2127]`}
          >
            <LogOut className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </Sidebar>
  );
}

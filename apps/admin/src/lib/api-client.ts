import { env } from '@/config/env';
import { VerifyResponse } from '@/features/auth/types/main';
import { setAuthTokens } from '@/features/auth/utils/main';
import { cookie } from '@/lib/cookie';
import axios, { InternalAxiosRequestConfig } from 'axios';

const handleKickout = (err: unknown) => {
  console.error(err);
  cookie.remove('access_token');
  cookie.remove('refresh_token');
  window.location.href = '/auth/login';
};

const axiosRequestInterceptor = (config: InternalAxiosRequestConfig<any>) => {
  const authToken = cookie.get('access_token');

  if (authToken) {
    config.headers.Authorization = `bearer ${authToken}`;
  }
  return config;
};

export const axiosErrorInterpreter = async (
  err: any,
  finalAction?: () => void,
) => {
  if (err.response) {
    const authToken = cookie.get('access_token');
    const refreshToken = cookie.get('refresh_token');

    if (authToken && err.response.status === 401) {
      // token expired user
      if (
        err.response.data?.message === 'jwt expired' ||
        err.response.data?.message === 'jwt malformed'
      ) {
        try {
          const response = await axios.get(`${env.API_URL}/auth/refresh`, {
            headers: { Authorization: `Bearer ${refreshToken}` },
          });

          setAuthTokens(response?.data as unknown as VerifyResponse);

          if (finalAction) {
            finalAction();
          } else {
            // Retry the original failed request
            const originalRequest = err.config;
            originalRequest.headers['Authorization'] =
              `Bearer ${response?.data?.data?.accessToken}`;
            return axios(originalRequest);
          }
        } catch (err) {
          handleKickout(err);
        }
      }

      // unauthorized user
      if (
        err.response.data?.message === 'Unauthorized' &&
        window.location.pathname !== '/auth/login'
      ) {
        handleKickout(err);
      }
    }
  }
};

export const axiosResponseErrorInterceptor = async (err: any) => {
  axiosErrorInterpreter(err);
  return Promise.reject(err);
};

export const api = axios.create({
  baseURL: env.API_URL,
});

api.interceptors.request.use(
  (config) => axiosRequestInterceptor(config),
  (err) => Promise.reject(err),
);

api.interceptors.response.use((response) => {
  const { data } = response;
  return data;
}, axiosResponseErrorInterceptor);

import { switchGitWorkspace } from '@/utils/git-utils';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface BaseState {
  workspaceId: string | null;
  setWorkspaceId: (id: string | null) => void;
  supabaseCodeVerifier: string | null;
  setSupabaseCodeVerifier: (verifier: string | null) => void;
  moduleId: string | null; // New: Represents the currently selected/active module
  setModuleId: (id: string | null) => void; // New: Action to set it
  clearAll: () => void; // New: Action to clear all state on logout
}

export const useBaseStore = create<BaseState>()(
  persist(
    (set) => ({
      workspaceId: null,
      // Modified: Reset moduleId when workspaceId changes and handle git workspace switching
      setWorkspaceId: (id) => {
        const currentWorkspaceId = useBaseStore.getState().workspaceId;

        // Switch git workspace (clear current, initialize new)
        if (id) {
          switchGitWorkspace(currentWorkspaceId, id);
        }

        set({ workspaceId: id, moduleId: null });
      },
      supabaseCodeVerifier: null,
      setSupabaseCodeVerifier: (verifier) =>
        set({ supabaseCodeVerifier: verifier }),
      moduleId: null, // Initialize new state
      setModuleId: (id) => set({ moduleId: id }), // Implement new action
      // Clear all state to default values (used on logout)
      clearAll: () =>
        set({
          workspaceId: null,
          supabaseCodeVerifier: null,
          moduleId: null,
        }),
    }),
    {
      name: 'base-storage', // name of the item in the storage (must be unique)
    },
  ),
);

import { api } from '@/lib/api-client';
import { toast } from '@/lib/toast';
import { tryCatch } from '@/lib/try-catch';
import { default as FS } from '@isomorphic-git/lightning-fs';
import { <PERSON><PERSON>er } from 'buffer';
import * as git from 'isomorphic-git';

export interface GitPull {
  success: boolean;
  workspaceId: string;
  fileCount: number;
  files: Record<string, string>;
}

// Make Buffer available globally for isomorphic-git
if (typeof window !== 'undefined') {
  window.Buffer = Buffer;
}

export const DEFAULT_GIT_DIR = '/project';

/**
 * Initializes and returns a new LightningFS instance.
 * @param workspaceId The unique identifier for the filesystem.
 * @returns A new FS instance.
 */
export const initializeFs = (workspaceId: string): FS => {
  return new FS(workspaceId);
};

/**
 * Clears all git filesystem storage data for a specific workspace.
 * This will delete the Lightning-FS IndexedDB database associated with the workspace,
 * removing all git repositories, files, and commit history.
 * @param workspaceId The unique identifier for the git filesystem to clear.
 * @returns A promise that resolves when the git storage is cleared.
 */
export const clearGitWorkspaceStorage = async (
  workspaceId: string,
): Promise<void> => {
  try {
    // Create a new FS instance with the wipe option to clear all git data
    new FS(workspaceId, { wipe: true });
  } catch {
    // Don't throw the error to prevent logout from failing
    // The git storage clearing is a cleanup operation and shouldn't block logout
  }
};

/**
 * Clears ALL git filesystem storage data for all workspaces that the user has accessed.
 * This function fetches all user workspaces and clears their Lightning-FS IndexedDB databases.
 * Used during logout to ensure complete cleanup of all git storage.
 * @returns A promise that resolves when all git storage is cleared.
 */
export const clearAllGitWorkspaceStorage = async (): Promise<void> => {
  try {
    // We need to get all workspace IDs that the user has access to
    // This requires an API call to get user's workspaces
    const { api } = await import('@/lib/api-client');
    const workspaces = await api.get('/workspace');

    if (workspaces && Array.isArray(workspaces)) {
      // Clear git storage for each workspace in parallel
      const clearPromises = workspaces.map((workspace: { id: string }) =>
        clearGitWorkspaceStorage(workspace.id)
      );

      await Promise.allSettled(clearPromises); // Use allSettled to not fail if one workspace fails
    }
  } catch {
    // Don't throw the error to prevent logout from failing
    // The git storage clearing is a cleanup operation and shouldn't block logout
  }
};

/**
 * @interface InitGitRepoArgs
 * @property fs The LightningFS instance.
 * @property dir The directory to initialize the Git repository in.
 */
export interface InitGitRepoArgs {
  fs: FS;
  dir?: string;
}

/**
 * Initializes a Git repository in the specified directory if it doesn't already exist.
 * @param args - {@link InitGitRepoArgs}
 */
/**
 * @interface IsGitRepoInitializedArgs
 * @property fs The LightningFS instance.
 * @property dir The directory to check for Git repository.
 */
export interface IsGitRepoInitializedArgs {
  fs: FS;
  dir?: string;
}

/**
 * Checks if a Git repository is already initialized in the specified directory.
 * @param args - {@link IsGitRepoInitializedArgs}
 * @returns A boolean indicating whether the Git repository is initialized.
 */
export const isGitRepoInitialized = async (
  args: IsGitRepoInitializedArgs,
): Promise<boolean> => {
  const { fs, dir = DEFAULT_GIT_DIR } = args;

  const { data: gitDirStat, error: statError } = await tryCatch(
    fs.promises.stat(`${dir}/.git`),
  );

  if (statError && (statError as any)?.code !== 'ENOENT') {
    // If stat fails for a reason other than "not found", throw the error
    console.error('Failed to check for .git directory:', statError);
    toast.error(
      'Failed to check Git repository: Could not check for existing .git directory.',
    );
    throw statError;
  }

  return !!gitDirStat; // Return true if .git directory exists
};

export const initGitRepo = async (args: InitGitRepoArgs): Promise<void> => {
  const { fs, dir = DEFAULT_GIT_DIR } = args;

  // Check if git is already initialized using the new function
  const isInitialized = await isGitRepoInitialized({ fs, dir });

  if (isInitialized) {
    return; // Already initialized
  }

  // Initialize Git repository
  try {
    await git.init({ fs, dir });
  } catch (initError) {
    console.error('Failed to initialize Git repository:', initError);
    toast.error(
      `Failed to initialize Git repository: ${initError instanceof Error ? initError.message : 'Unknown error'}`,
    );
    throw initError;
  }
};

/**
 * @interface EnsureDirectoryExistsArgs
 * @property fs The LightningFS instance.
 * @property baseDir The base directory.
 * @property relativeDirPath The path of the directory to ensure, relative to the baseDir.
 */
export interface EnsureDirectoryExistsArgs {
  fs: FS;
  baseDir?: string;
  relativeDirPath: string;
}

/**
 * Ensures that a directory exists within the virtual filesystem.
 * If it doesn't exist, it creates the directory and any necessary parent directories.
 * @param args - {@link EnsureDirectoryExistsArgs}
 */
export const ensureDirectoryExists = async (
  args: EnsureDirectoryExistsArgs,
): Promise<void> => {
  const { fs, baseDir = DEFAULT_GIT_DIR, relativeDirPath } = args;
  const fullPath = relativeDirPath.startsWith(baseDir)
    ? relativeDirPath
    : `${baseDir}/${relativeDirPath}`.replace(/\/\//g, '/'); // Normalize path

  // Avoid trying to create the root or base directory itself if relativeDirPath is empty or '/'
  if (
    fullPath === baseDir ||
    fullPath === `${baseDir}/` ||
    relativeDirPath === '' ||
    relativeDirPath === '/'
  ) {
    try {
      await fs.promises.stat(baseDir); // Ensure baseDir itself exists
    } catch (e) {
      if ((e as any)?.code === 'ENOENT') {
        await fs.promises.mkdir(baseDir); // Create base if it doesn't exist (recursive handled by ensureDirectoryExists logic)
      } else {
        throw e; // Re-throw other errors
      }
    }
    return;
  }

  try {
    await fs.promises.stat(fullPath);
  } catch (e) {
    if ((e as any)?.code === 'ENOENT') {
      // Directory doesn't exist, create it recursively
      const parentDirFullPath = fullPath.substring(
        0,
        fullPath.lastIndexOf('/'),
      );

      // Ensure parent directory exists, unless we're at the baseDir level
      if (
        parentDirFullPath &&
        parentDirFullPath !== baseDir &&
        parentDirFullPath !== ''
      ) {
        const parentRelativePath = parentDirFullPath.startsWith(baseDir + '/')
          ? parentDirFullPath.substring(baseDir.length + 1)
          : parentDirFullPath;

        if (parentRelativePath !== '') {
          // Avoid infinite loop for root-like paths
          await ensureDirectoryExists({
            fs,
            baseDir,
            relativeDirPath: parentRelativePath,
          });
        }
      }
      await fs.promises.mkdir(fullPath);
    } else {
      // Another error occurred
      console.error(`Error checking or creating directory ${fullPath}:`, e);
      toast.error(`Failed to ensure directory ${relativeDirPath} exists.`);
      throw e;
    }
  }
};

/**
 * @interface WriteFileArgs
 * @property fs The LightningFS instance.
 * @property baseDir The base directory.
 * @property relativeFilePath The path of the file to write, relative to the baseDir.
 * @property content The content to write to the file.
 */
export interface WriteFileArgs {
  fs: FS;
  baseDir?: string;
  relativeFilePath: string;
  content: string;
}

/**
 * Writes a file to the specified path within the virtual filesystem.
 * Automatically creates parent directories if they don't exist.
 * @param args - {@link WriteFileArgs}
 * @returns The full path of the written file.
 */
export const writeFile = async (args: WriteFileArgs): Promise<string> => {
  const { fs, baseDir = DEFAULT_GIT_DIR, relativeFilePath, content } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  const fullPath = relativeFilePath.startsWith(baseDir)
    ? relativeFilePath
    : `${baseDir}/${relativeFilePath}`.replace(/\/\//g, '/'); // Normalize path

  // Ensure parent directories exist
  const parentDirPath = fullPath.substring(0, fullPath.lastIndexOf('/'));
  if (parentDirPath && parentDirPath !== baseDir && parentDirPath !== '') {
    const parentRelativePath = parentDirPath.startsWith(baseDir + '/')
      ? parentDirPath.substring(baseDir.length + 1)
      : parentDirPath; // Should ideally not be just baseDir due to earlier check
    await ensureDirectoryExists({
      fs,
      baseDir,
      relativeDirPath: parentRelativePath,
    });
  } else if (parentDirPath === '' && !baseDir.startsWith('/')) {
    // Handle cases where the file is in the root of a relative baseDir
    // e.g. baseDir 'project', relativeFilePath 'file.txt' -> parentDirPath ''
    // We still need to ensure the baseDir itself exists.
    await ensureDirectoryExists({ fs, baseDir, relativeDirPath: '' }); // Pass empty string to ensure baseDir
  }

  // Write the file
  try {
    await fs.promises.writeFile(fullPath, content);
    return fullPath;
  } catch (error) {
    console.error(`Failed to write file ${fullPath}:`, error);
    toast.error(
      `Failed to write file ${relativeFilePath}: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
    throw error;
  }
};

/**
 * @interface ReadFileArgs
 * @property fs The LightningFS instance.
 * @property baseDir The base directory.
 * @property relativeFilePath The path of the file to read, relative to the baseDir.
 */
export interface ReadFileArgs {
  fs: FS;
  baseDir?: string;
  relativeFilePath: string;
}

/**
 * Reads a file from the specified path within the virtual filesystem.
 * @param args - {@link ReadFileArgs}
 * @returns The content of the file as a string.
 */
export const readFile = async (args: ReadFileArgs): Promise<string> => {
  const { fs, baseDir = DEFAULT_GIT_DIR, relativeFilePath } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  const fullPath = relativeFilePath.startsWith(baseDir)
    ? relativeFilePath
    : `${baseDir}/${relativeFilePath}`.replace(/\/\//g, '/'); // Normalize path

  try {
    const contentBuffer = await fs.promises.readFile(fullPath);
    // Assuming content is UTF-8. LightningFS readFile returns Uint8Array.
    const content = new TextDecoder().decode(contentBuffer);
    return content;
  } catch (error) {
    console.error(`Failed to read file ${fullPath}:`, error);
    toast.error(
      `Failed to read file ${relativeFilePath}: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
    throw error;
  }
};

// More utility functions will be added here based on the refactoring plan.
/**
 * @interface CreateGitCommitArgs
 * @property fs The LightningFS instance.
 * @property dir The base directory of the Git repository.
 * @property message The commit message.
 * @property author Optional author details.
 */
export interface CreateGitCommitArgs {
  fs: FS;
  dir?: string;
  message: string;
  author?: { name: string; email: string };
}

/**
 * Creates a Git commit with the current staged changes.
 * @param args - {@link CreateGitCommitArgs}
 * @returns The commit SHA (string) if successful, or null if there were no changes to commit or an error occurred.
 */
export const createGitCommit = async (
  args: CreateGitCommitArgs,
): Promise<string | null> => {
  const {
    fs,
    dir = DEFAULT_GIT_DIR,
    message,
    author = { name: 'AI Generator', email: '<EMAIL>' },
  } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  try {
    // Get status to see what files have changed in the working directory
    const initialStatus = await git.statusMatrix({ fs, dir });
    const filesToStage = initialStatus
      .filter((row: git.StatusRow) => {
        // const headStatus: git.HeadStatus = row[1]; // Not directly needed for the final logic
        const workdirStatus: git.WorkdirStatus = row[2];
        const stageStatus: git.StageStatus = row[3];

        // Condition 1: Stage is absent, but Workdir has content.
        // This means the file is new in workdir, or was staged for deletion and then re-added/modified.
        // Examples: a.txt [0,2,0], j.txt [1,2,0], k.txt [1,1,0]
        if (stageStatus === 0 && workdirStatus !== 0) {
          return true;
        }

        // Condition 2: Stage is identical to HEAD, but Workdir is different from HEAD.
        // This means the file is modified in workdir or deleted from workdir, and these changes are not yet staged.
        // Examples: e.txt [1,2,1], h.txt [1,0,1]
        if (stageStatus === 1 && workdirStatus !== 1) {
          // workdirStatus !== 1 implies workdirStatus is 0 (deleted) or 2 (modified from HEAD)
          return true;
        }

        // Condition 3: Stage is different from Workdir.
        // This means the file was staged, but then further modified in workdir.
        // Examples: c.txt [0,2,3], g.txt [1,2,3]
        if (stageStatus === 3) {
          return true;
        }

        // Otherwise, the stage already accurately reflects the workdir state for this file,
        // or there are no changes to stage (e.g., unmodified file, or file fully staged).
        // Examples: b.txt [0,2,2], d.txt [1,1,1], f.txt [1,2,2], i.txt [1,0,0]
        return false;
      })
      .map((row: git.StatusRow) => row[0]); // Get the filepath

    if (filesToStage.length === 0) {
      // toast.info('No changes to commit.');
      return null; // Nothing to commit
    }

    // Stage only the identified files
    for (const filepath of filesToStage) {
      await git.add({ fs, dir, filepath });
    }

    // Create the commit
    const sha = await git.commit({
      fs,
      dir,
      message,
      author,
    });

    // toast.success(`Commit created: ${message.substring(0, 30)}...`);
    return sha;
  } catch (error) {
    console.error('Failed to create commit:', error);
    toast.error(
      `Failed to create commit: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
    return null;
  }
};
/**
 * @interface GetAllProjectFilesArgs
 * @property fs The LightningFS instance.
 * @property baseDir The base directory to scan.
 */
export interface GetAllProjectFilesArgs {
  fs: FS;
  baseDir?: string;
}

/**
 * Retrieves all files and their content from the specified directory within the virtual filesystem.
 * Ignores the .git directory.
 * @param args - {@link GetAllProjectFilesArgs}
 * @returns A Record where keys are relative file paths and values are file contents.
 */
export const getAllProjectFiles = async (
  args: GetAllProjectFilesArgs,
): Promise<Record<string, string>> => {
  const { fs, baseDir = DEFAULT_GIT_DIR } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  const files: Record<string, string> = {};

  // Recursive function to walk directory
  const walkDir = async (currentPath: string) => {
    const entries = await fs.promises.readdir(currentPath);

    for (const entry of entries) {
      const fullEntryPath = `${currentPath}/${entry}`.replace(/\/\//g, '/');

      // Skip .git directory
      if (entry === '.git' || fullEntryPath.includes('/.git/')) {
        continue;
      }

      const stats = await fs.promises.stat(fullEntryPath);

      if (stats.isDirectory()) {
        await walkDir(fullEntryPath);
      } else {
        // Path relative to the initial baseDir for the keys in the returned record
        const relativeFilePath = fullEntryPath.startsWith(baseDir + '/')
          ? fullEntryPath.substring(baseDir.length + 1)
          : fullEntryPath.substring(baseDir.length); // Handles if baseDir is '/'

        // Use the readFile utility from this file
        const content = await readFile({ fs, baseDir, relativeFilePath });
        files[relativeFilePath] = content;
      }
    }
  };

  try {
    await walkDir(baseDir);
    return files;
  } catch (error) {
    console.error(`Failed to get all project files from ${baseDir}:`, error);
    // toast.error(
    //   `Failed to retrieve project files: ${error instanceof Error ? error.message : 'Unknown error'}`,
    // );
    return {}; // Return empty object on error
  }
};
/**
 * @interface PushToGitHubRepoArgs
 * @property fs The LightningFS instance.
 * @property dir The base directory of the Git repository.
 * @property workspaceId The ID of the workspace.
 * @property squash If true, squashes all local commits into a single one for the push. Defaults to true.
 */
export interface PushToGitHubRepoArgs {
  fs: FS;
  dir?: string;
  workspaceId: string;
  squash?: boolean;
}

/**
 * @interface GitHubPushPayload
 * @property workspaceId The ID of the workspace
 * @property files Files to push as a record of path-content pairs
 * @property commitMessage Commit message for the push
 * @property squash Whether to squash commits
 */
export interface GitHubPushPayload {
  workspaceId: string;
  files: Record<string, string>;
  commitMessage: string;
  squash: boolean;
}

/**
 * Pushes changes to a GitHub repository via a backend API.
 * Can push all files as a single squashed commit or (not fully implemented here) individual commits.
 * When squash is enabled (default), after a successful push, the local repository is reset to match
 * the remote state with a single commit, avoiding redundant commit history.
 * @param args - {@link PushToGitHubRepoArgs}
 * @returns The URL of the repository if successful, otherwise null.
 */
export const pushToGitHubRepo = async (
  args: PushToGitHubRepoArgs,
): Promise<string | null> => {
  const { fs, dir = DEFAULT_GIT_DIR, workspaceId, squash = true } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  try {
    const localCommits = await git.log({ fs, dir });
    if (localCommits.length <= 1) {
      // Only first commit exists
      toast.error('No new commits to push (only initial commit exists).');
      return null;
    }

    // Get all files changed since first commit (which represents the remote state)
    const modifiedFiles = new Set<string>();

    // Compare each commit against its parent to find modified files
    // Skip the first commit (last in the array) as it represents the remote state
    for (let i = 0; i < localCommits.length - 1; i++) {
      const commit = localCommits[i];

      // Skip if no parent commit
      if (!commit.commit.parent || commit.commit.parent.length === 0) {
        continue;
      }

      const parentOid = commit.commit.parent[0];

      // Use the shared utility function to get changed files
      const changedFilesInCommit = await getChangedFilesBetweenCommits({
        fs,
        dir,
        currentOid: commit.oid,
        parentOid,
      });

      // Add all changed files to the modifiedFiles set
      changedFilesInCommit.all.forEach((file) => modifiedFiles.add(file));
    }

    // Get current content of modified files
    const filesToPush: Record<string, string> = {};
    for (const filePath of modifiedFiles) {
      try {
        filesToPush[filePath] = await readFile({
          fs,
          baseDir: dir, // Change from dir to baseDir to match interface
          relativeFilePath: filePath,
        });
      } catch {
        // File was deleted, include it with empty content
        filesToPush[filePath] = '';
      }
    }

    if (Object.keys(filesToPush).length === 0) {
      toast.info('No modified files found to push.');
      return null;
    }

    const files = filesToPush; // Rename to match existing payload structure

    // Generate commit message from all commits except the first
    const commitMessages = localCommits
      .slice(0, -1) // Exclude first commit
      .map((c) => c.commit.message.trim())
      .filter((msg) => msg);
    const commitMessage =
      commitMessages.length > 0
        ? commitMessages.join('; ')
        : 'AI-generated updates';

    const payload: GitHubPushPayload = {
      workspaceId: workspaceId,
      files,
      commitMessage,
      squash,
    };

    // Send to backend
    const { data, error } = await tryCatch(api.post('/github/push', payload));

    // Assuming tryCatch returns the direct data object on success
    const responseData = data as any;

    if (error || !responseData?.repoUrl) {
      console.error(
        'Failed to push to GitHub via API:',
        error || 'No repoUrl in response',
      );
      toast.error(
        `Failed to push to GitHub: ${(error as any)?.message || responseData?.message || 'API error'}`,
      );
      return null;
    }

    const { repoUrl } = responseData;

    // After successful push, simplify the repository to a single commit
    if (squash && localCommits.length > 1) {
      try {
        // Step 1: Save current files to temporary storage
        const currentFiles = await getAllProjectFiles({ fs, baseDir: dir });

        // Step 2: Reset to initial commit
        await git.checkout({
          fs,
          dir,
          ref: localCommits[localCommits.length - 1].oid,
          force: true,
        });

        // Step 3: Remove all existing files (to handle deletions properly)
        const existingFiles = await git.listFiles({ fs, dir });
        for (const filepath of existingFiles) {
          try {
            await fs.promises.unlink(`${dir}/${filepath}`);
          } catch (error) {
            console.error(`Error removing file ${filepath}:`, error);
          }
        }

        // Step 4: Write all current files back to the filesystem
        for (const [filePath, content] of Object.entries(currentFiles)) {
          // Skip .git files
          if (filePath.startsWith('.git/')) continue;

          // Only write non-empty files (empty content means file was deleted)
          if (content.trim() !== '') {
            try {
              await writeFile({
                fs,
                baseDir: dir,
                relativeFilePath: filePath,
                content,
              });
            } catch (writeError) {
              console.error(`Error writing file ${filePath}:`, writeError);
            }
          }
        }

        // Step 5: Create a single commit with all changes
        await createGitCommit({
          fs,
          dir,
          message: `Current state: ${commitMessage}`,
          author: { name: 'AI Generator', email: '<EMAIL>' },
        });

        // toast.success('Repository simplified to a single commit');
      } catch (error) {
        console.error('Error squashing commits:', error);
        toast.error(
          'Push succeeded but commit squashing failed. Repository may need manual cleanup.',
        );
      }
    }

    // toast.success('Successfully pushed to GitHub!');
    return repoUrl;
  } catch (error) {
    console.error('Error during pushToGitHubRepo:', error);
    toast.error(
      `Failed to push to GitHub: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
    return null;
  }
};
/**
 * @interface PullFromGitHubRepoArgs
 * @property fs The LightningFS instance.
 * @property dir The base directory of the Git repository.
 * @property workspaceId The ID of the workspace, used for API call and commit message.
 * @property repoPath Optional path within the repository to pull from.
 * @property recursive Optional flag for recursive pull (if API supports).
 */
export interface PullFromGitHubRepoArgs {
  fs: FS;
  dir?: string;
  workspaceId: string;
  repoPath?: string;
  recursive?: boolean;
}

/**
 * Pulls files from a GitHub repository via a backend API and updates the local filesystem.
 * Creates a commit to record the pulled changes.
 * @param args - {@link PullFromGitHubRepoArgs}
 * @returns True if successful, false otherwise.
 */
export const pullFromGitHubRepo = async (
  args: PullFromGitHubRepoArgs,
): Promise<boolean> => {
  const {
    fs,
    dir = DEFAULT_GIT_DIR,
    workspaceId,
    repoPath = '',
    recursive = true,
  } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  try {
    // Call backend API
    const { data, error } = await tryCatch(
      api.get<GitPull>('/github/pull', {
        params: {
          workspaceId,
          path: repoPath,
          recursive,
        },
      }),
    );

    const responseData = data as GitPull | null; // Type assertion

    if (error || !responseData?.success) {
      console.error(
        'Failed to pull from GitHub via API:',
        error || 'API call unsuccessful or no data.',
      );
      toast.error(
        `Failed to pull from GitHub: ${(error as any)?.message || 'API call unsuccessful or no data.'}`,
      );
      return false;
    }

    if (!responseData.files || Object.keys(responseData.files).length === 0) {
      toast.info('No files were pulled from GitHub.');
      // If no files were pulled, do not attempt to create a commit.
      // This prevents unrelated local changes from being committed with a "pull: no files changed" message.
      return true; // Considered successful if API call was, even if no files.
    }

    // Write each file to the local filesystem
    // Ensure baseDir (dir) is used correctly with writeFile
    for (const [relativeFilePath, fileContent] of Object.entries(
      responseData.files,
    )) {
      // Assuming fileContent is string. If it can be other types, add handling.
      await writeFile({
        fs,
        baseDir: dir,
        relativeFilePath,
        content: fileContent as string,
      });
    }

    // Create a commit to record the pulled changes
    const commitMessage = `Pulled changes from GitHub repository: ${workspaceId}`;
    const commitSha = await createGitCommit({
      fs,
      dir,
      message: commitMessage,
    });

    if (!commitSha) {
      // toast.info(
      //   'Pulled from GitHub, but no effective changes to commit locally.',
      // );
      // This can happen if pulled files match existing local files exactly after staging.
    } else {
      // toast.success(
      //   `Successfully pulled from GitHub and committed as ${commitSha.substring(0, 7)}.`,
      // );
    }
    return true;
  } catch (error) {
    console.error('Error during pullFromGitHubRepo:', error);
    toast.error(
      `Failed to pull from GitHub: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
    return false;
  }
};

/**
 * @interface GetChangedFilesBetweenCommitsArgs
 * @property fs The LightningFS instance.
 * @property dir The base directory of the Git repository.
 * @property currentOid The SHA-1 object id of the current commit.
 * @property parentOid The SHA-1 object id of the parent commit.
 */
export interface GetChangedFilesBetweenCommitsArgs {
  fs: FS;
  dir?: string;
  currentOid: string;
  parentOid: string;
}

/**
 * @interface FileChanges
 * @property added Array of file paths that were added in the current commit.
 * @property modified Array of file paths that were modified in the current commit.
 * @property deleted Array of file paths that were deleted in the current commit.
 * @property all Set of all changed file paths (union of added, modified, and deleted).
 */
export interface FileChanges {
  added: string[];
  modified: string[];
  deleted: string[];
  all: Set<string>;
}

/**
 * Gets the files that changed between two commits.
 * @param args - {@link GetChangedFilesBetweenCommitsArgs}
 * @returns A promise that resolves to a FileChanges object with details about added, modified, and deleted files.
 */
export const getChangedFilesBetweenCommits = async (
  args: GetChangedFilesBetweenCommitsArgs,
): Promise<FileChanges> => {
  const { fs, dir = DEFAULT_GIT_DIR, currentOid, parentOid } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  try {
    // Get all file paths from both commits
    const [currentFiles, parentFiles] = await Promise.all([
      git.listFiles({ fs, dir, ref: currentOid }),
      git.listFiles({ fs, dir, ref: parentOid }),
    ]);

    // Create sets for faster lookup
    const currentFilesSet = new Set(currentFiles);
    const parentFilesSet = new Set(parentFiles);

    // Find added files (in current but not in parent)
    const addedFiles = currentFiles.filter((file) => !parentFilesSet.has(file));

    // Find deleted files (in parent but not in current)
    const deletedFiles = parentFiles.filter(
      (file) => !currentFilesSet.has(file),
    );

    // Find potentially modified files (exist in both commits)
    const commonFiles = currentFiles.filter((file) => parentFilesSet.has(file));
    const modifiedFiles: string[] = [];

    // Check each common file to see if content changed
    for (const filepath of commonFiles) {
      try {
        const [currentBlob, parentBlob] = await Promise.all([
          git.readBlob({ fs, dir, oid: currentOid, filepath }),
          git.readBlob({ fs, dir, oid: parentOid, filepath }),
        ]);

        // Compare the content hashes (OIDs)
        if (currentBlob.oid !== parentBlob.oid) {
          modifiedFiles.push(filepath);
        }
      } catch (error) {
        // If we can't read a blob, assume it was modified
        console.warn(`Error comparing file ${filepath}:`, error);
        modifiedFiles.push(filepath);
      }
    }

    // Create a set of all changed files
    const allChangedFiles = new Set([
      ...addedFiles,
      ...modifiedFiles,
      ...deletedFiles,
    ]);

    return {
      added: addedFiles,
      modified: modifiedFiles,
      deleted: deletedFiles,
      all: allChangedFiles,
    };
  } catch (error) {
    console.error(`Failed to get changed files between commits:`, error);
    return { added: [], modified: [], deleted: [], all: new Set() };
  }
};

/**
 * @interface GetCommitFileCountArgs
 * @property fs The LightningFS instance.
 * @property dir The base directory of the Git repository.
 * @property tree The SHA-1 object id of the tree object to list files from.
 * @property oid The SHA-1 object id of the commit.
 * @property parent Array of parent commit SHA-1 object ids.
 */
export interface GetCommitFileCountArgs {
  fs: FS;
  dir?: string;
  tree: string;
  oid?: string;
  parent?: string[];
}

/**
 * Retrieves the number of files changed in a specific commit.
 * For the first commit (no parent), counts all files.
 * For other commits, counts only files that changed compared to parent.
 *
 * @param args - {@link GetCommitFileCountArgs}
 * @returns A promise that resolves to the number of changed files in the commit.
 */
export const getCommitFileCount = async (
  args: GetCommitFileCountArgs,
): Promise<number> => {
  const { fs, dir = DEFAULT_GIT_DIR, tree, oid, parent } = args;
  if (!fs) throw new Error('Filesystem (fs) not initialized');

  try {
    // If no parent exists (first commit) or parent info not provided, count all files
    if (!parent || parent.length === 0 || !oid) {
      const files = await git.listFiles({ fs, dir, ref: tree });
      return files.length;
    }

    const parentOid = parent[0]; // Use first parent for merge commits

    // Use the shared utility function to get changed files
    const changedFiles = await getChangedFilesBetweenCommits({
      fs,
      dir,
      currentOid: oid,
      parentOid,
    });

    // Return the count of changed files
    return changedFiles.all.size;
  } catch (error) {
    console.error(`Failed to count changed files for commit ${oid}:`, error);
    return 0;
  }
};

/**
 * @interface GetAllCommitsArgs
 * @property fs The LightningFS instance.
 * @property dir The base directory of the Git repository.
 * @property depth Optional. The number of commits to retrieve.
 * @property since Optional. Retrieve commits more recent than a JavaScript Date object.
 */
export interface GetAllCommitsArgs {
  fs: FS;
  dir?: string;
  depth?: number;
  since?: Date;
}

/**
 * Retrieves all commits (or a subset based on options) from the Git repository.
 * @param args - {@link GetAllCommitsArgs}
 * @returns An array of commit objects if successful, or an empty array if an error occurs or no commits are found.
 */
export const getAllCommits = async (
  args: GetAllCommitsArgs,
): Promise<git.ReadCommitResult[]> => {
  const { fs, dir = DEFAULT_GIT_DIR, depth, since } = args;
  if (!fs) {
    toast.error('Filesystem (fs) not initialized for getAllCommits.');
    throw new Error('Filesystem (fs) not initialized');
  }

  try {
    const commits = await git.log({ fs, dir, depth, since });
    if (commits.length === 0) {
      // toast.info('No commits found in the repository.');
    } else {
      toast.success(`Retrieved ${commits.length} commits.`);
    }
    return commits;
  } catch (error) {
    console.error('Failed to get commits:', error);
    toast.error(
      `Failed to retrieve commits: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
    return []; // Return empty array on error
  }
};
